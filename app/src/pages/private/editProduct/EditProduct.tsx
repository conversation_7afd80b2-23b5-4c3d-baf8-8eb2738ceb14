import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import productFormSchemaJson from '@/formSchemas/productForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import {
  useCategories,
  useSubcategoriesByCategory,
  useProduct,
  useUpdateProduct
} from '@/hooks/useCatalog';
import { UpdateProductRequest } from '@/types/catalog.types';
import { editProductRoute } from '@/routes/private/editProduct.route';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './EditProduct.css';
import Button from '@/components/ui/Button';

const EditProduct: React.FC = () => {
  const navigate = useNavigate();
  // Type assertion to handle the search parameters
  const search = useSearch({ from: editProductRoute.id }) as { id: string };
  const id = search.id;
  const toast = useRef<ToastRef>(null);

  // State for form schema
  const [productFormSchema, setProductFormSchema] = useState<FormSchema>(productFormSchemaJson as FormSchema);

  // Validate id parameter on component mount
  useEffect(() => {
    if (!id) {
      toast.current?.showError('Product ID is required');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [id, navigate]);

  // State for selected category
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);



  // Fetch product data
  const { data: productData, isLoading: isProductLoading, isError: isProductError } = useProduct(id);
  const product = productData?.data;

  // Handle error in fetching product data
  useEffect(() => {
    if (isProductError) {
      toast.current?.showError('Failed to fetch product data');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [isProductError, navigate]);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // Fetch subcategories based on selected category
  const { data: subcategoriesData } = useSubcategoriesByCategory(selectedCategoryId);
  const subcategories = subcategoriesData?.data || [];

  // Update product mutation
  const updateProductMutation = useUpdateProduct();

  // Set initial category ID when product data is loaded
  useEffect(() => {
    if (product && product.categoryId && !selectedCategoryId) {
      setSelectedCategoryId(product.categoryId);
    }
  }, [product, selectedCategoryId]);

  // Update form schema with categories and subcategories
  useEffect(() => {
    const updatedSchema = { ...productFormSchema };

    // Update category options
    const categoryField = updatedSchema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: category.id,
      }));
    }

    // Update subcategory options
    const subcategoryField = updatedSchema.fields.find(field => field.name === 'subCategoryId');
    if (subcategoryField) {
      subcategoryField.options = subcategories.map(subcategory => ({
        label: subcategory.name,
        value: subcategory.id,
      }));
    }

    // Update submit button label
    const submitAction = updatedSchema.actions.find(action => action.id === 'submit');
    if (submitAction) {
      submitAction.label = 'Update Product';
    }

    setProductFormSchema(updatedSchema);
  }, [categories, subcategories]);

  // Handle category change
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    if (field === 'categoryId') {
      handleCategoryChange(value);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    if (!product) return;

    try {
      // Prepare product data
      const productData: UpdateProductRequest = {
        name: data.name,
        description: data.description,
        price: parseFloat(data.price),
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : data.categoryId,
        subCategoryId: typeof data.subCategoryId === 'string' ? parseInt(data.subCategoryId) : data.subCategoryId,
        organizationId: 40928446087168, // Use the test organization ID
        imageFiles: data.imageFiles || null,
      };

      // Update product
      await updateProductMutation.mutateAsync({
        id: product.id!,
        data: productData,
      });

      // Show success message
      toast.current?.showSuccess('Product updated successfully');

      // Navigate back to catalog
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error updating product:', error);

      // Show error message
      toast.current?.showError('Failed to update product');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };



  // Prepare default values for the form
  const getDefaultValues = () => {
    if (!product) return {};

    return {
      name: product.name,
      description: product.description || '',
      categoryId: product.categoryId,
      subCategoryId: product.subCategoryId,
      price: product.price,
      imageFiles: null, // Start with no files selected for editing
    };
  };

  if (isProductLoading) {
    return (
      <div className="edit-product p-4">
        <Card
          title="Edit Product"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="flex justify-content-center">
            <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
          </div>
        </Card>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="edit-product p-4">
        <Card
          title="Edit Product"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="text-center">
            <p>Product not found.</p>
            <Button
              variant="primary"
              onClick={handleCancel}
              className="mt-3"
            >
              Back to Catalog
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="edit-product p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Edit Product"
        subtitle="Update product details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          key={product?.id} // Force re-render when product changes
          schema={productFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
        />
      </Card>
    </div>
  );
};

export default EditProduct;
